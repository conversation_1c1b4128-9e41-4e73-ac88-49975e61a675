<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationStyle": "custom",
    "backgroundColor": "#f8f8f8"
  }
}
</route>

<script lang="ts" setup>
defineOptions({
  name: 'Login',
})

// 响应式数据
const loading = ref(false)
const title = ref('证件照制作')
const userInfo = ref({
  avatar: '',
  nickname: '',
  openid: '',
})

// 微信登录
async function handleWechatLogin() {
  try {
    loading.value = true

    // 获取微信登录code
    const loginRes = await uni.login({
      provider: 'weixin',
    })

    if (loginRes.errMsg === 'login:ok') {
      console.log('微信登录成功，code:', loginRes.code)

      // 获取用户信息
      const userRes = await uni.getUserProfile({
        desc: '用于完善用户资料',
      })

      if (userRes.errMsg === 'getUserProfile:ok') {
        userInfo.value = {
          avatar: userRes.userInfo.avatarUrl,
          nickname: userRes.userInfo.nickName,
          openid: '', // 这里需要通过后端接口获取
        }

        // TODO: 调用后端接口，传递code获取openid和session_key
        // const backendRes = await api.login({ code: loginRes.code, userInfo: userRes.userInfo })

        uni.showToast({
          title: '登录成功',
          icon: 'success',
        })

        // 登录成功后跳转到首页
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index',
          })
        }, 1500)
      }
    }
  }
  catch (error) {
    console.error('登录失败:', error)
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'none',
    })
  }
  finally {
    loading.value = false
  }
}

// 暂不登录，直接返回
function handleGuestMode() {
  uni.navigateBack()
}

console.log('login page loaded')
</script>

<template>
  <view class="relative h-screen w-full overflow-hidden from-white to-[#e6e9ff] bg-gradient-to-br">
    <!-- 装饰圆形 -->
    <view class="absolute h-100 w-100 rounded-full from-[rgba(78,71,253,0.15)] to-[rgba(78,71,253,0)] bg-gradient-radial -right-37.5 -top-37.5" />
    <view class="absolute h-125 w-125 rounded-full from-[rgba(78,71,253,0.15)] to-[rgba(78,71,253,0)] bg-gradient-radial -bottom-62.5 -left-62.5" />

    <view class="relative z-1 h-[calc(100vh-88rpx)] w-full center flex-col from-transparent to-[rgba(240,242,255,0.5)] bg-gradient-to-b">
      <!-- 欢迎文字 -->
      <view class="center flex-col">
        <text class="text-10 text-[#333] font-bold">
          {{ title }}
        </text>
        <text class="mt-5 text-7 text-[#666]">
          让证件照制作更简单
        </text>
      </view>

      <!-- 登录按钮 -->
      <wd-button
        type="info"
        block
        custom-style="width: 600rpx; height: 88rpx; border-radius: 44rpx; margin-top: 80rpx; background: #8280FF; border-color: #8280FF; color: #ffffff;"
        :loading="loading"
        @click="handleWechatLogin"
      >
        快捷登录
      </wd-button>

      <!-- 暂不登录 -->
      <view class="mt-15 text-center">
        <text class="text-7 text-[#666] underline" @click="handleGuestMode">
          暂不登录
        </text>
      </view>
    </view>
  </view>
</template>
