<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "登录",
    "navigationStyle": "default",
    "backgroundColor": "#f8f8f8"
  }
}
</route>

<script lang="ts" setup>
defineOptions({
  name: 'Login',
})

// 响应式数据
const loading = ref(false)
const userInfo = ref({
  avatar: '',
  nickname: '',
  openid: '',
})

// 微信登录
async function handleWechatLogin() {
  try {
    loading.value = true

    // 获取微信登录code
    const loginRes = await uni.login({
      provider: 'weixin',
    })

    if (loginRes.errMsg === 'login:ok') {
      console.log('微信登录成功，code:', loginRes.code)

      // 获取用户信息
      const userRes = await uni.getUserProfile({
        desc: '用于完善用户资料',
      })

      if (userRes.errMsg === 'getUserProfile:ok') {
        userInfo.value = {
          avatar: userRes.userInfo.avatarUrl,
          nickname: userRes.userInfo.nickName,
          openid: '', // 这里需要通过后端接口获取
        }

        // TODO: 调用后端接口，传递code获取openid和session_key
        // const backendRes = await api.login({ code: loginRes.code, userInfo: userRes.userInfo })

        uni.showToast({
          title: '登录成功',
          icon: 'success',
        })

        // 登录成功后跳转到首页
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index',
          })
        }, 1500)
      }
    }
  }
  catch (error) {
    console.error('登录失败:', error)
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'none',
    })
  }
  finally {
    loading.value = false
  }
}

// 游客模式
function handleGuestMode() {
  uni.showModal({
    title: '提示',
    content: '游客模式下部分功能受限，建议登录后使用完整功能',
    confirmText: '继续',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        uni.switchTab({
          url: '/pages/index/index',
        })
      }
    },
  })
}

console.log('login page loaded')
</script>

<template>
  <view class="login-container">
    <!-- 顶部logo区域 -->
    <view class="logo-section">
      <image
        src="/static/logo.svg"
        alt="logo"
        class="logo-image"
      />
      <view class="app-title">
        证件照制作
      </view>
      <view class="app-subtitle">
        专业证件照，一键生成
      </view>
    </view>

    <!-- 登录区域 -->
    <view class="login-section">
      <!-- 微信登录按钮 -->
      <button
        class="wechat-login-btn"
        :loading="loading"
        @click="handleWechatLogin"
      >
        <view class="btn-content">
          <text class="wechat-icon">
            👤
          </text>
          <text class="btn-text">
            微信快捷登录
          </text>
        </view>
      </button>

      <!-- 游客模式 -->
      <view class="guest-section">
        <text class="guest-text" @click="handleGuestMode">
          暂不登录，以游客身份浏览
        </text>
      </view>
    </view>

    <!-- 底部说明 -->
    <view class="footer-section">
      <view class="privacy-text">
        登录即表示同意
        <text class="link-text">
          《用户协议》
        </text>
        和
        <text class="link-text">
          《隐私政策》
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 60rpx 40rpx 40rpx;
}

.logo-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.logo-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  padding: 20rpx;
}

.app-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-top: 40rpx;
}

.app-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 16rpx;
}

.login-section {
  margin-top: 120rpx;
}

.wechat-login-btn {
  width: 100%;
  height: 88rpx;
  background: #07c160;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(7, 193, 96, 0.3);
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.wechat-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.btn-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 500;
}

.guest-section {
  text-align: center;
  margin-top: 60rpx;
}

.guest-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: underline;
}

.footer-section {
  margin-top: auto;
  padding-top: 60rpx;
}

.privacy-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  line-height: 1.5;
}

.link-text {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: underline;
}
</style>
