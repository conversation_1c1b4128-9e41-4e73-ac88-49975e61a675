<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationStyle": "custom",
    "backgroundColor": "#f8f8f8"
  }
}
</route>

<script lang="ts" setup>
defineOptions({
  name: 'Login',
})

// 响应式数据
const loading = ref(false)
const title = ref('证件照制作')
const userInfo = ref({
  avatar: '',
  nickname: '',
  openid: '',
})

// 微信登录
async function handleWechatLogin() {
  try {
    loading.value = true

    // 获取微信登录code
    const loginRes = await uni.login({
      provider: 'weixin',
    })

    if (loginRes.errMsg === 'login:ok') {
      console.log('微信登录成功，code:', loginRes.code)

      // 获取用户信息
      const userRes = await uni.getUserProfile({
        desc: '用于完善用户资料',
      })

      if (userRes.errMsg === 'getUserProfile:ok') {
        userInfo.value = {
          avatar: userRes.userInfo.avatarUrl,
          nickname: userRes.userInfo.nickName,
          openid: '', // 这里需要通过后端接口获取
        }

        // TODO: 调用后端接口，传递code获取openid和session_key
        // const backendRes = await api.login({ code: loginRes.code, userInfo: userRes.userInfo })

        uni.showToast({
          title: '登录成功',
          icon: 'success',
        })

        // 登录成功后跳转到首页
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index',
          })
        }, 1500)
      }
    }
  }
  catch (error) {
    console.error('登录失败:', error)
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'none',
    })
  }
  finally {
    loading.value = false
  }
}

// 暂不登录，直接返回
function handleGuestMode() {
  uni.navigateBack()
}

console.log('login page loaded')
</script>

<template>
  <view class="container">
    <view class="login-content">
      <!-- 欢迎文字 -->
      <view class="welcome-text">
        <text style="font-size: 40rpx; font-weight: bold; color: #333;">
          {{ title }}
        </text>
        <text style="font-size: 28rpx; color: #666; margin-top: 20rpx;">
          让证件照制作更简单
        </text>
      </view>

      <!-- 登录按钮 -->
      <wd-button
        type="info"
        block
        custom-style="width: 600rpx; height: 88rpx; border-radius: 44rpx; margin-top: 80rpx; background: #8280FF; border-color: #8280FF; color: #ffffff;"
        :loading="loading"
        @click="handleWechatLogin"
      >
        快捷登录
      </wd-button>

      <!-- 暂不登录 -->
      <view class="guest-section">
        <text class="guest-text" @click="handleGuestMode">
          暂不登录
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #fff 0%, #e6e9ff 100%);
  position: relative;
  overflow: hidden;
}

.container::before {
  content: '';
  position: absolute;
  width: 400rpx;
  height: 400rpx;
  background: radial-gradient(circle, rgba(78, 71, 253, 0.15) 0%, rgba(78, 71, 253, 0) 70%);
  top: -150rpx;
  right: -150rpx;
  border-radius: 50%;
}

.container::after {
  content: '';
  position: absolute;
  width: 500rpx;
  height: 500rpx;
  background: radial-gradient(circle, rgba(78, 71, 253, 0.15) 0%, rgba(78, 71, 253, 0) 70%);
  bottom: -250rpx;
  left: -250rpx;
  border-radius: 50%;
}

.login-content {
  width: 100%;
  height: calc(100vh - 88rpx);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(240, 242, 255, 0.5) 100%);
}

.welcome-text {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.guest-section {
  text-align: center;
  margin-top: 60rpx;
}

.guest-text {
  font-size: 28rpx;
  color: #666;
  text-decoration: underline;
}
</style>
